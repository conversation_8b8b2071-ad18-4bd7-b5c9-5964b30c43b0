# Syrix Project Essential Guide

Full-stack Microsoft 365 security platform - Focus on essentials for <PERSON> assistance.

## 🚨 CRITICAL RULES - ALWAYS FOLLOW

### 1. **NEVER Answer Without Checking First**
- **"Did you add X?"** → Check files first
- **"Is Y implemented?"** → Search codebase first  
- **"What's the status?"** → Validate actual state first
- **Always use tools BEFORE answering about current state**

### 2. **Focus on Current Request**
- Address exactly what user asks NOW
- Ask for clarification if unclear
- Don't assume continuation of previous work
- Confirm understanding before proceeding

### 3. **Syrix Development Methodology**
- **NEVER use @ApplicationScoped** - use @PolicyRemediator pattern only
- Package namespace: `io.syrix.*` (NOT com.syrix)
- Start responses with "Remembering..." when loading context

## Core Architecture

### Modules & Paths
- **Root**: `/Users/<USER>/Documents/Development/private/syrix/`
- **SyrixBackend**: Quarkus Java 21 (Port 8989) - Microsoft 365 security engine
- **SyrixWEB/SyrixPortal**: React 19 + TypeScript (Port 3030/8080) - Main dashboard  
- **SyrixCommon**: Shared models (SyrixDM), MongoDB DAO (SyrixDAO), Messaging
- **Additional**: SyrixDAO at `/Users/<USER>/Documents/Development/private/syrix/syrixdao`, SyrixDM at `/Users/<USER>/Documents/Development/private/syrix/syrixdm`

### Tech Stack
**Backend**: Java 21, Quarkus 3.23.0, MSAL4J, Graph API, OPA/Rego, LangChain4j, MongoDB  
**Frontend**: React 19, TypeScript, Redux Toolkit, Tailwind CSS, shadcn/ui  
**Testing**: JUnit 5/Mockito (backend), Playwright (frontend)

## Development Patterns

### Frontend Patterns
- shadcn/ui components in `/src/components/ui/`
- `cn()` utility for className management
- Dark/light mode with `isDarkMode` prop
- Redux hooks from `/src/app/hooks.ts`
- Feature-based structure: `/src/features/{feature-name}/`

### Backend Patterns
```java
@PolicyRemediator("MS.{SERVICE}.{SECTION}.{POLICY}v{VERSION}")
public class ExampleRemediator extends RemediatorBase {
    // NO @ApplicationScoped - use constructor injection
}
```
- Service layer: `io.syrix.products.microsoft.*`
- CompletableFuture for async operations
- Generic DAO pattern with MongoDB

### Session Management
- HTTP Session-based with UUID company isolation
- BaseApiController.getCurrentCompanyId(session)
- All services accept UUID companyId parameters
- Ready for multi-tenant data filtering

## OAuth & Microsoft Integration

### Token Generation Factory Pattern
```java
// Certificate-based (Application)
factory.getMGraphTokenGenerator(true, params)

// Refresh token-based (Delegated)  
factory.getMGraphTokenGenerator(false, params)
```

**Scopes**: Graph, Management, Outlook, SharePoint, Teams  
**Multi-Cloud**: Commercial, GCC, GCC High, DOD support

## JIRA-to-Code Automation

**Location**: `/.claude/commands/syrix-jira-to-code.md` (v3.8.2)

**Usage**: `/syrix-jira-to-code jira_item=SYRIX-123 project_path="/path/to/syrix"`

**Key Features**:
- 10-phase workflow with MCP integration
- 5-barrier execution system preventing hallucination
- @PolicyRemediator pattern generation
- CISA compliance validation
- Dynamic domain research

## Memory System with Memento

- Use semantic_search for finding relevant information
- Track code entities with relations  
- Update knowledge graph after implementations
- Store technical decisions and patterns

## Quick Reference

### Service Structure
```
Backend: /io/syrix/products/microsoft/{service}/
         /io/syrix/protocols/  (API clients)
         /io/syrix/reports/     (Report generation)

Frontend: /src/features/       (Business features)
          /src/components/ui/   (shadcn/ui components)
          /src/app/            (Redux store)
```

### Testing & Validation
- **Frontend**: Playwright at localhost:3030
- **Backend**: JUnit 5 with Mockito
- **Validation**: Always compile-check after changes
- **In-place edits**: Use filesystem tools

### Common Tasks
1. **Component Migration**: Convert to shadcn/ui with TypeScript
2. **Service Implementation**: Create Microsoft service integrations
3. **Policy Remediation**: 4-component bundle (Remediator, Service, Rego, Baseline)
4. **Session Management**: Company-scoped data filtering

## Development Workflow

1. **Analyze** codebase with filesystem tools
2. **Design** before implementation (Phase 4.5)
3. **Implement** following patterns
4. **Test** with appropriate tools
5. **Update** memory graph with changes

## Important Notes

- Always check actual files before answering status questions
- Focus on user's current request, not previous context
- Use MCP tool format: `mcp__server__tool` (not server:tool)
- Maintain backward compatibility when updating
- Document significant changes with version updates

---
*This is a condensed guide. For specific implementation details, analyze actual codebase files.*