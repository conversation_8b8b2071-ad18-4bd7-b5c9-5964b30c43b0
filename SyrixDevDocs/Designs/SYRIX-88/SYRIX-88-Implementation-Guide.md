# SYRIX-88 Implementation Guide
# MS.SHAREPOINT.1.5v1 SharePoint Guest Resharing Prevention

**JIRA**: SYRIX-88 | **Policy**: MS.SHAREPOINT.1.5v1 | **Generated**: Phase 8 Documentation

---

## 📖 Table of Contents
1. [Quick Start Guide](#quick-start-guide)
2. [Architecture Deep Dive](#architecture-deep-dive)
3. [API Reference](#api-reference)
4. [Testing Guide](#testing-guide)
5. [Troubleshooting](#troubleshooting)
6. [Performance Considerations](#performance-considerations)
7. [Integration Examples](#integration-examples)
8. [Maintenance & Monitoring](#maintenance--monitoring)

---

## 🚀 Quick Start Guide

### Prerequisites
- Syrix Backend running with SharePoint module enabled
- PowerShell SharePoint client configured with tenant admin privileges
- OPA compliance engine running for policy validation

### Basic Usage

```java
// Initialize remediator
PowerShellSharepointClient client = new PowerShellSharepointClient(config);
SharePointTenantProperties tenant = getSharePointTenant();
SPPreventGuestResharingRemediator remediator =
    new SPPreventGuestResharingRemediator(client, tenant, spConfig);

// Execute remediation
CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
result.thenAccept(outcome -> {
    if ("SUCCESS".equals(outcome.getStatus())) {
        logger.info("Guest resharing prevention enabled successfully");
    }
});

// Rollback if needed
CompletableFuture<PolicyChangeResult> rollback = remediator.rollback(result.get());
```

### Configuration Validation

```java
// Validate current configuration
SharePointConfigurationService configService = new SharePointConfigurationService();
ConfigurationResult config = configService.exportConfiguration();
JsonNode tenant = config.getData().get("SPO_tenant").get(0);

Boolean preventResharing = tenant.get("preventExternalUsersFromResharing").asBoolean();
logger.info("Current setting: {}", preventResharing ? "COMPLIANT" : "NON_COMPLIANT");
```

---

## 🏗️ Architecture Deep Dive

### Component Interaction Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Syrix Core    │───▶│  Configuration   │───▶│   Rego Policy   │
│   Scheduler     │    │     Service      │    │   Validation    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  @Policy        │    │  PowerShell      │    │  SharePoint     │
│  Remediator     │◀───│  SharePoint      │───▶│  Admin Center   │
│  SPPrevent...   │    │  Client          │    │  Tenant Props   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│   Audit Log     │
│   & Storage     │
└─────────────────┘
```

### Data Model Architecture

```java
// PowerShell Command Response
TenantProperties {
    Boolean preventExternalUsersFromResharing; // Raw from PowerShell
    String objectIdentity;                      // Tenant identifier
    Map<String, Object> additionalProperties;  // Extensible for future properties
}

// Syrix Internal Model
SharePointTenantProperties {
    Boolean preventExternalUsersFromResharing; // Mapped property
    // ... other 20+ SharePoint security properties
}

// Policy Change Tracking
ParameterChangeResult {
    String parameter = "PreventExternalUsersFromResharing";
    Object prevValue; // false (before remediation)
    Object newValue;  // true (after remediation)
    Instant timestamp;
}
```

### Thread Safety & Concurrency

The remediator is **thread-safe** with the following considerations:

```java
// Safe: Each remediator instance is stateless
CompletableFuture<PolicyChangeResult> future1 = remediator1.remediate_();
CompletableFuture<PolicyChangeResult> future2 = remediator2.remediate_();

// Safe: Multiple policies can run concurrently
CompletableFuture.allOf(
    preventGuestResharing.remediate_(),
    modernAuthRemediator.remediate_(),
    malwareProtection.remediate_()
).join();

// WARNING: Don't share PowerShellSharepointClient instances across threads
// Each remediator should have its own client instance
```

---

## 📋 API Reference

### SPPreventGuestResharingRemediator

#### Constructor Patterns

```java
// Standard constructor for normal remediation
public SPPreventGuestResharingRemediator(
    PowerShellSharepointClient client,
    SharePointTenantProperties tenant,
    SharepointRemediationConfig spConfig
)

// Rollback constructor (no config needed)
public SPPreventGuestResharingRemediator(
    PowerShellSharepointClient client,
    SharePointTenantProperties tenant
)
```

#### Core Methods

```java
// Primary remediation method
CompletableFuture<PolicyChangeResult> remediate_()
// Returns: SUCCESS if enabled, ALREADY_COMPLIANT if already true, FAILED on error

// Legacy JSON interface
CompletableFuture<JsonNode> remediate()
// Returns: JSON representation of PolicyChangeResult

// Rollback capability
CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult)
// Returns: SUCCESS if rolled back, FAILED on error

// Policy identification
String getPolicyId()
// Returns: "MS.SHAREPOINT.1.5v1"
```

#### Response Formats

```java
// Success Response
PolicyChangeResult {
    status: "SUCCESS"
    policyId: "MS.SHAREPOINT.1.5v1"
    message: "External user resharing prevention settings fixed"
    changes: [
        ParameterChangeResult {
            parameter: "PreventExternalUsersFromResharing"
            prevValue: false
            newValue: true
            timestamp: 2025-01-15T10:30:00Z
        }
    ]
}

// Already Compliant Response
PolicyChangeResult {
    status: "SUCCESS" // Note: Still success, but no changes made
    message: "External user resharing prevention settings fixed"
    changes: [] // Empty - no changes needed
}

// Failure Response
PolicyChangeResult {
    status: "FAILED"
    policyId: "MS.SHAREPOINT.1.5v1"
    message: "SharePoint tenant properties are not available"
    changes: null
}
```

---

## 🧪 Testing Guide

### Unit Test Structure

```java
@ExtendWith(MockitoExtension.class)
class SPPreventGuestResharingRemediatorTest {
    @Mock PowerShellSharepointClient mockClient;
    @Mock SharepointRemediationConfig mockConfig;

    SharePointTenantProperties tenant;
    SPPreventGuestResharingRemediator remediator;

    @BeforeEach
    void setUp() {
        tenant = new SharePointTenantProperties();
        tenant.objectIdentity = "test-tenant-12345";
        remediator = new SPPreventGuestResharingRemediator(mockClient, tenant, mockConfig);
    }

    @Test
    void testRemediationSuccess_WhenPropertyDisabled_ShouldEnableAndReturnSuccess() {
        // Given: Current setting is false (non-compliant)
        tenant.preventExternalUsersFromResharing = false;

        // Mock successful PowerShell command execution
        ShellCommandResult<GeneralResult> successResult = createSuccessResult();
        when(mockClient.execute_(any())).thenReturn(CompletableFuture.completedFuture(successResult));

        // When: Execute remediation
        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

        // Then: Should return success with parameter change
        assertThat(result.join().getStatus()).isEqualTo("SUCCESS");
        assertThat(result.join().getChanges()).hasSize(1);
        assertThat(result.join().getChanges().getFirst().getPrevValue()).isEqualTo(false);
        assertThat(result.join().getChanges().getFirst().getNewValue()).isEqualTo(true);
    }
}
```

### Integration Test Examples

```java
@SpringBootTest
@TestPropertySource(properties = {
    "syrix.sharepoint.test-mode=true",
    "syrix.powershell.mock-client=true"
})
class SPPreventGuestResharingIntegrationTest {

    @Autowired SharePointConfigurationService configService;
    @Autowired SharePointRemediationOrchestrator orchestrator;

    @Test
    void testFullWorkflow_ConfigurationToRemediation() {
        // 1. Get current configuration
        ConfigurationResult config = configService.exportConfiguration();
        JsonNode tenant = config.getData().get("SPO_tenant").get(0);

        // 2. Verify property exists and is accessible
        assertThat(tenant.has("preventExternalUsersFromResharing")).isTrue();

        // 3. If non-compliant, trigger remediation
        if (!tenant.get("preventExternalUsersFromResharing").asBoolean()) {
            PolicyRemediationRequest request = new PolicyRemediationRequest();
            request.setPolicyId("MS.SHAREPOINT.1.5v1");
            request.setTenantId(tenant.get("objectIdentity").asText());

            PolicyChangeResult result = orchestrator.remediate(request).join();
            assertThat(result.getStatus()).isEqualTo("SUCCESS");
        }

        // 4. Verify Rego policy validation passes
        RegoValidationResult regoResult = regoValidator.validate(tenant, "SharepointConfig.rego");
        Optional<JsonNode> policy = findPolicyResult(regoResult, "MS.SHAREPOINT.1.5v1");
        assertThat(policy).isPresent();
        assertThat(policy.get().get("RequirementMet").asBoolean()).isTrue();
    }
}
```

### Mock Data Factories

```java
public class SharePointTestDataFactory {

    public static SharePointTenantProperties compliantTenant() {
        SharePointTenantProperties tenant = new SharePointTenantProperties();
        tenant.objectIdentity = "compliant-tenant-12345";
        tenant.preventExternalUsersFromResharing = true; // Compliant
        return tenant;
    }

    public static SharePointTenantProperties nonCompliantTenant() {
        SharePointTenantProperties tenant = new SharePointTenantProperties();
        tenant.objectIdentity = "non-compliant-tenant-67890";
        tenant.preventExternalUsersFromResharing = false; // Non-compliant
        return tenant;
    }

    public static ShellCommandResult<GeneralResult> successCommandResult() {
        GeneralResult result = new GeneralResult();
        result.errorInfo = null; // No error indicates success

        ShellCommandResult<GeneralResult> commandResult = new ShellCommandResult<>();
        commandResult.setData(List.of(result));
        return commandResult;
    }

    public static ShellCommandResult<GeneralResult> failureCommandResult(String errorMessage) {
        GeneralResult result = new GeneralResult();
        result.errorInfo = new ErrorInfo();
        result.errorInfo.errorMessage = errorMessage;

        ShellCommandResult<GeneralResult> commandResult = new ShellCommandResult<>();
        commandResult.setData(List.of(result));
        return commandResult;
    }
}
```

---

## 🔍 Troubleshooting

### Common Issues & Solutions

#### Issue: "SharePoint tenant properties are not available"

**Symptoms:**
```java
PolicyChangeResult {
    status: "FAILED"
    message: "SharePoint tenant properties are not available"
}
```

**Root Causes:**
1. SharePointConfigurationService failed to retrieve tenant properties
2. PowerShell authentication issues
3. Insufficient SharePoint admin privileges

**Solutions:**
```java
// Check configuration service health
ConfigurationResult config = configService.exportConfiguration();
if (config.getData() == null || !config.getData().has("SPO_tenant")) {
    logger.error("Configuration service not returning SharePoint tenant data");
    // Verify PowerShell client authentication
    // Check SharePoint admin center connectivity
}

// Verify tenant object construction
SharePointTenantProperties tenant = new SharePointTenantProperties(tenantProps, allowSPO, allowODB);
if (tenant.objectIdentity == null) {
    logger.error("Tenant object identity missing - PowerShell query may have failed");
}
```

#### Issue: PowerShell Command Execution Failures

**Symptoms:**
```java
PolicyChangeResult {
    status: "FAILED"
    message: "Failed to prevent guest resharing: Command execution failed"
}
```

**Debugging:**
```java
// Enable debug logging
<logger name="io.syrix.products.microsoft.sharepoint" level="DEBUG"/>

// Check PowerShell command construction
SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(
    tenant.objectIdentity,
    "PreventExternalUsersFromResharing",
    true,
    false
);
logger.debug("PowerShell command XML: {}", command.getXmlBody());

// Verify retry mechanism
Retry.executeWithRetry(() -> {
    logger.debug("Attempting PowerShell command execution, attempt: {}", attemptNumber);
    return client.execute_(command);
}, MAX_RETRY);
```

#### Issue: Rollback Failures

**Symptoms:**
```java
PolicyChangeResult {
    status: "FAILED"
    message: "Rollback failed: Previous value is null, cannot perform rollback"
}
```

**Analysis:**
```java
// Validate fixResult structure
if (fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
    logger.error("Fix result has no changes to rollback");
    return; // Cannot rollback without change history
}

ParameterChangeResult change = fixResult.getChanges().getFirst();
logger.debug("Rollback data - Previous: {}, New: {}",
    change.getPrevValue(), change.getNewValue());

// Ensure parameter change was properly tracked during initial remediation
if (change.getPrevValue() == null) {
    logger.error("Original value not captured during remediation");
    // This indicates a bug in the remediation process
}
```

### Performance Troubleshooting

#### Slow Remediation Performance

**Monitoring:**
```java
// Add timing metrics
Instant start = Instant.now();
PolicyChangeResult result = remediator.remediate_().join();
Duration duration = Duration.between(start, Instant.now());
logger.info("Remediation completed in {} ms", duration.toMillis());

// Monitor PowerShell command execution time
CompletableFuture<ShellCommandResult<GeneralResult>> command =
    client.execute_(spCommand)
    .orTimeout(30, TimeUnit.SECONDS); // Add timeout monitoring
```

**Typical Performance Benchmarks:**
- **Normal**: 2-5 seconds for single tenant property change
- **Slow**: 10-15 seconds (investigate network/auth issues)
- **Timeout**: >30 seconds (PowerShell client or SharePoint service issues)

---

## ⚡ Performance Considerations

### Memory Usage

```java
// Memory-efficient patterns
public class SPPreventGuestResharingRemediator {
    // Use static references for constants
    private static final String PROPERTY_NAME = "PreventExternalUsersFromResharing";
    private static final Logger log = LoggerFactory.getLogger(SPPreventGuestResharingRemediator.class);

    // Minimize object creation in hot paths
    private CompletableFuture<PolicyChangeResult> runCommand(boolean value, Boolean prevValue) {
        try {
            // Reuse command builder pattern
            SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(
                tenant.objectIdentity, PROPERTY_NAME, value, prevValue
            );

            // Use efficient async patterns
            return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
                    .thenApply(this::checkResult); // Method reference for efficiency
        } catch (Exception ex) {
            // Avoid string concatenation in hot paths
            log.error("Run command for policy {} failed", getPolicyId(), ex);
            return CompletableFuture.failedFuture(ex);
        }
    }
}
```

### Concurrency Optimization

```java
// Batch multiple SharePoint policy remediations
CompletableFuture<Void> batchRemediation = CompletableFuture.allOf(
    preventGuestResharing.remediate_(),
    modernAuthEnforcement.remediate_(),
    malwareProtection.remediate_()
).thenRun(() -> {
    logger.info("All SharePoint policies remediated successfully");
});

// Use separate PowerShell clients for parallel execution
PowerShellSharepointClient client1 = new PowerShellSharepointClient(config);
PowerShellSharepointClient client2 = new PowerShellSharepointClient(config);
PowerShellSharepointClient client3 = new PowerShellSharepointClient(config);

// Each client can handle one concurrent operation safely
```

### Resource Management

```java
@Component
public class SharePointRemediationPool {
    private final ExecutorService executorService;
    private final PowerShellClientPool clientPool;

    @PostConstruct
    public void initialize() {
        // Dedicated thread pool for SharePoint operations
        this.executorService = Executors.newFixedThreadPool(
            5, // Max 5 concurrent SharePoint operations
            new ThreadFactoryBuilder()
                .setNameFormat("sharepoint-remediation-%d")
                .setDaemon(true)
                .build()
        );

        // PowerShell client pool to avoid authentication overhead
        this.clientPool = new PowerShellClientPool(
            10, // Pool size
            Duration.ofMinutes(5) // Client reuse timeout
        );
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        clientPool.close();
    }
}
```

---

## 🔗 Integration Examples

### Spring Boot Integration

```java
@Service
public class SharePointComplianceService {

    @Autowired
    private SharePointConfigurationService configService;

    @Autowired
    private PowerShellSharepointClient powershellClient;

    @Value("${syrix.sharepoint.remediation.auto-fix:false}")
    private boolean autoRemediation;

    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void dailyComplianceCheck() {
        try {
            ConfigurationResult config = configService.exportConfiguration();
            JsonNode tenant = config.getData().get("SPO_tenant").get(0);

            // Check MS.SHAREPOINT.1.5v1 compliance
            boolean isCompliant = tenant.get("preventExternalUsersFromResharing").asBoolean();

            if (!isCompliant && autoRemediation) {
                logger.info("Non-compliant SharePoint tenant detected, initiating auto-remediation");

                SharePointTenantProperties tenantProps = buildTenantProperties(tenant);
                SPPreventGuestResharingRemediator remediator =
                    new SPPreventGuestResharingRemediator(powershellClient, tenantProps, spConfig);

                PolicyChangeResult result = remediator.remediate_().join();

                if ("SUCCESS".equals(result.getStatus())) {
                    notificationService.sendComplianceNotification(
                        "SharePoint guest resharing prevention enabled automatically"
                    );
                } else {
                    alertService.sendAlert(
                        AlertLevel.HIGH,
                        "Failed to auto-remediate SharePoint compliance: " + result.getMessage()
                    );
                }
            }

        } catch (Exception ex) {
            logger.error("Daily SharePoint compliance check failed", ex);
            alertService.sendAlert(AlertLevel.MEDIUM, "SharePoint compliance check failed: " + ex.getMessage());
        }
    }
}
```

### REST API Integration

```java
@RestController
@RequestMapping("/api/v1/sharepoint/policies")
public class SharePointPolicyController {

    @Autowired
    private SharePointRemediationOrchestrator orchestrator;

    @PostMapping("/MS.SHAREPOINT.1.5v1/remediate")
    public CompletableFuture<ResponseEntity<PolicyChangeResult>> remediateGuestResharing(
            @RequestParam String tenantId,
            @RequestParam(defaultValue = "false") boolean dryRun) {

        if (dryRun) {
            // Dry run mode - only validate current state
            return orchestrator.validatePolicy("MS.SHAREPOINT.1.5v1", tenantId)
                    .thenApply(validation -> ResponseEntity.ok(
                        PolicyChangeResult.builder()
                            .status(validation.isCompliant() ? "COMPLIANT" : "NON_COMPLIANT")
                            .policyId("MS.SHAREPOINT.1.5v1")
                            .message("Dry run: " + validation.getMessage())
                            .build()
                    ));
        } else {
            // Execute actual remediation
            return orchestrator.remediate("MS.SHAREPOINT.1.5v1", tenantId)
                    .thenApply(result -> {
                        HttpStatus status = "SUCCESS".equals(result.getStatus()) ?
                            HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR;
                        return ResponseEntity.status(status).body(result);
                    });
        }
    }

    @PostMapping("/MS.SHAREPOINT.1.5v1/rollback")
    public CompletableFuture<ResponseEntity<PolicyChangeResult>> rollbackGuestResharing(
            @RequestBody PolicyChangeResult originalResult) {

        return orchestrator.rollback("MS.SHAREPOINT.1.5v1", originalResult)
                .thenApply(result -> {
                    HttpStatus status = "SUCCESS".equals(result.getStatus()) ?
                        HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR;
                    return ResponseEntity.status(status).body(result);
                });
    }
}
```

### Monitoring Integration

```java
@Component
public class SharePointPolicyMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter remediationAttempts;
    private final Counter remediationSuccesses;
    private final Timer remediationDuration;

    public SharePointPolicyMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.remediationAttempts = Counter.builder("sharepoint.remediation.attempts")
                .tag("policy", "MS.SHAREPOINT.1.5v1")
                .register(meterRegistry);
        this.remediationSuccesses = Counter.builder("sharepoint.remediation.successes")
                .tag("policy", "MS.SHAREPOINT.1.5v1")
                .register(meterRegistry);
        this.remediationDuration = Timer.builder("sharepoint.remediation.duration")
                .tag("policy", "MS.SHAREPOINT.1.5v1")
                .register(meterRegistry);
    }

    public void recordRemediationAttempt() {
        remediationAttempts.increment();
    }

    public void recordRemediationSuccess() {
        remediationSuccesses.increment();
    }

    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

---

## 🔧 Maintenance & Monitoring

### Health Check Implementation

```java
@Component
public class SharePointPolicyHealthIndicator implements HealthIndicator {

    @Autowired
    private SharePointConfigurationService configService;

    @Override
    public Health health() {
        try {
            // Test configuration service connectivity
            ConfigurationResult config = configService.exportConfiguration();

            if (config.getData() == null || !config.getData().has("SPO_tenant")) {
                return Health.down()
                        .withDetail("error", "SharePoint tenant data unavailable")
                        .withDetail("timestamp", Instant.now())
                        .build();
            }

            JsonNode tenant = config.getData().get("SPO_tenant").get(0);
            boolean hasProperty = tenant.has("preventExternalUsersFromResharing");

            if (!hasProperty) {
                return Health.down()
                        .withDetail("error", "PreventExternalUsersFromResharing property missing")
                        .withDetail("tenant", tenant.get("objectIdentity").asText())
                        .build();
            }

            return Health.up()
                    .withDetail("tenant", tenant.get("objectIdentity").asText())
                    .withDetail("policy", "MS.SHAREPOINT.1.5v1")
                    .withDetail("preventExternalUsersFromResharing",
                        tenant.get("preventExternalUsersFromResharing").asBoolean())
                    .withDetail("lastCheck", Instant.now())
                    .build();

        } catch (Exception ex) {
            return Health.down(ex)
                    .withDetail("error", ex.getMessage())
                    .withDetail("timestamp", Instant.now())
                    .build();
        }
    }
}
```

### Logging Configuration

```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- SharePoint remediation specific logging -->
    <logger name="io.syrix.products.microsoft.sharepoint.remediation.SPPreventGuestResharingRemediator"
            level="INFO" additivity="false">
        <appender-ref ref="SHAREPOINT_REMEDIATION"/>
    </logger>

    <!-- PowerShell client logging -->
    <logger name="io.syrix.protocols.client.sharepoint.powershell"
            level="WARN" additivity="false">
        <appender-ref ref="POWERSHELL_CLIENT"/>
    </logger>

    <!-- Separate appender for SharePoint operations -->
    <appender name="SHAREPOINT_REMEDIATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/sharepoint-remediation.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/sharepoint-remediation.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - [Policy: %X{policyId}] [Tenant: %X{tenantId}] %msg%n</pattern>
        </encoder>
    </appender>
</configuration>
```

### Monitoring Queries

```sql
-- Remediation success rate (last 24 hours)
SELECT
    policy_id,
    COUNT(*) as total_attempts,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successes,
    ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM policy_remediation_log
WHERE policy_id = 'MS.SHAREPOINT.1.5v1'
    AND created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY policy_id;

-- Average remediation time
SELECT
    AVG(duration_ms) as avg_duration_ms,
    MIN(duration_ms) as min_duration_ms,
    MAX(duration_ms) as max_duration_ms
FROM policy_remediation_log
WHERE policy_id = 'MS.SHAREPOINT.1.5v1'
    AND status = 'SUCCESS'
    AND created_at >= NOW() - INTERVAL 7 DAY;

-- Rollback frequency
SELECT
    DATE(created_at) as date,
    COUNT(*) as rollback_count
FROM policy_rollback_log
WHERE policy_id = 'MS.SHAREPOINT.1.5v1'
    AND created_at >= NOW() - INTERVAL 30 DAY
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

---

## 📊 Appendices

### A. PowerShell Command Reference

```powershell
# Get current setting
Get-SPOTenant | Select-Object PreventExternalUsersFromResharing

# Enable guest resharing prevention
Set-SPOTenant -PreventExternalUsersFromResharing $True

# Disable guest resharing prevention (for rollback)
Set-SPOTenant -PreventExternalUsersFromResharing $False

# Verify change applied
Get-SPOTenant | Where-Object {$_.PreventExternalUsersFromResharing -eq $True}
```

### B. Rego Policy Reference

```rego
package sharepoint

# MS.SHAREPOINT.1.5v1 validation rule
tests contains {
    "PolicyId": "MS.SHAREPOINT.1.5v1",
    "Criticality": "Shall",
    "Commandlet": ["Get-SPOTenant", "Get-PnPTenant"],
    "ActualValue": [Tenant.PreventExternalUsersFromResharing],
    "ReportDetails": ReportDetailsBoolean(Status),
    "RequirementMet": Status
} if {
    Status := Tenant.PreventExternalUsersFromResharing == true
}

# Helper function for boolean reporting
ReportDetailsBoolean(true) = "Requirement met: External users are prevented from resharing"
ReportDetailsBoolean(false) = "Requirement not met: External users can reshare content"
```

### C. Error Codes Reference

| Error Code | Description | Resolution |
|------------|-------------|------------|
| SP-1001 | Tenant properties unavailable | Check PowerShell client connection |
| SP-1002 | Command execution timeout | Verify SharePoint service availability |
| SP-1003 | Insufficient permissions | Ensure SharePoint admin privileges |
| SP-1004 | Property mapping failure | Verify tenant properties structure |
| SP-1005 | Rollback data missing | Check parameter change tracking |

---

**Document Version**: 1.0
**Created**: 2025-01-15
**Phase**: 8 - Documentation Generation
**Status**: Implementation Complete