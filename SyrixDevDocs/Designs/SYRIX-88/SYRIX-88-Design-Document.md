# SYRIX-88 Design Document
# MS.SHAREPOINT.1.5v1 - PreventExternalUsersFromResharing Implementation

**JIRA Issue**: SYRIX-88
**Type**: Security Remediation Story
**CISA Policy ID**: MS.SHAREPOINT.1.5v1
**CIS Benchmark**: 7.2.5 - Ensure external users are prevented from re-sharing files, folders, and sites they do not own

---

## 📋 Executive Summary

This design document outlines the implementation of a complete 4-component + baseline security remediation bundle to prevent external users from re-sharing SharePoint content they don't own. The implementation follows Syrix @PolicyRemediator methodology and integrates with existing SharePoint infrastructure.

### Security Impact
- **Risk Mitigation**: Prevents guest users from resharing SharePoint content beyond original sharing intent
- **Compliance**: Aligns with CIS Microsoft 365 Foundations Benchmark 7.2.5
- **Data Protection**: Reduces data leakage risk through uncontrolled guest sharing

---

## 🏗️ Architecture Overview

### Component Structure (5 Components)
1. **Data Model Extensions** - TenantProperties & SharePointTenantProperties
2. **@PolicyRemediator Implementation** - SPPreventGuestResharingRemediator
3. **Rego Policy Validation** - SharepointConfig.rego update
4. **Configuration Service Integration** - Automatic tenant property capture
5. **CISA Baseline Documentation** - sharepoint.md baseline update

### Integration Points
- **PowerShell Client**: SPShellCommand.PnPTenant.SET() for Set-SPOTenant execution
- **SharePoint Configuration Service**: Automatic property retrieval via existing tenant discovery
- **OPA Compliance Engine**: Rego policy validation integration
- **Audit System**: Full remediation result tracking and storage

---

## 🔧 Technical Implementation

### Component 1: Data Model Extensions

#### TenantProperties.java (Line 32)
```java
public Boolean preventExternalUsersFromResharing;
```

#### SharePointTenantProperties.java (Lines 42, 79)
```java
// Property declaration
public Boolean preventExternalUsersFromResharing;

// Constructor mapping
this.preventExternalUsersFromResharing = tenant.preventExternalUsersFromResharing;
```

#### SharepointConstants.java (Lines 49-50)
```java
public static final String PREVENT_EXTERNAL_USERS_FROM_RESHARING_PROPERTY = "PreventExternalUsersFromResharing";
public static final String PREVENT_RESHARING_SUCCESS = "External user resharing prevention settings fixed";
```

### Component 2: @PolicyRemediator Implementation

#### SPPreventGuestResharingRemediator.java
```java
@PolicyRemediator("MS.SHAREPOINT.1.5v1")
public class SPPreventGuestResharingRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {

    private static final Logger log = LoggerFactory.getLogger(SPPreventGuestResharingRemediator.class);

    // Standard constructor pattern
    public SPPreventGuestResharingRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
        super(client, tenant, spConfig);
    }

    // Rollback constructor
    public SPPreventGuestResharingRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        super(client, tenant, null);
    }

    // Core remediation logic with early exit pattern
    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        if (tenant == null) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties are not available")
            );
        }

        Boolean currentValue = tenant.preventExternalUsersFromResharing;

        // Early exit if already compliant
        if (Boolean.TRUE.equals(currentValue)) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.success_(getPolicyId(), PREVENT_RESHARING_SUCCESS)
            );
        }

        return runCommand(true, currentValue)
                .exceptionally(ex -> {
                    log.error("Remediate policy {} finished with exception", getPolicyId(), ex);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to prevent guest resharing: " + ex.getMessage());
                });
    }

    // PowerShell command execution
    private CompletableFuture<PolicyChangeResult> runCommand(boolean preventResharing, Boolean prevValue) {
        try {
            SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(
                tenant.objectIdentity,
                PREVENT_EXTERNAL_USERS_FROM_RESHARING_PROPERTY,
                preventResharing,
                prevValue
            );

            return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
                    .thenApply(this::checkResult);
        } catch (Exception ex) {
            log.error("Run command for policy {} failed", getPolicyId(), ex);
            return CompletableFuture.failedFuture(ex);
        }
    }

    // Full rollback implementation
    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        try {
            if (fixResult == null || fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "No changes to rollback")
                );
            }

            if (tenant == null) {
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties not available for rollback")
                );
            }

            ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
            Boolean previousValue = Boolean.valueOf(changeResult.getPrevValue().toString());
            Boolean newValue = Boolean.valueOf(changeResult.getNewValue().toString());

            return runCommand(previousValue != null ? previousValue : false, newValue)
                    .exceptionally(ex -> {
                        log.error("Rollback policy {} finished with exception", getPolicyId(), ex);
                        return IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage());
                    });
        } catch (Exception ex) {
            log.error("Rollback policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage())
            );
        }
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
    }
}
```

### Component 3: Rego Policy Validation

#### SharepointConfig.rego Addition
```rego
tests contains {
    "PolicyId": "MS.SHAREPOINT.1.5v1",
    "Criticality": "Shall",
    "Commandlet": ["Get-SPOTenant", "Get-PnPTenant"],
    "ActualValue": [Tenant.PreventExternalUsersFromResharing],
    "ReportDetails": ReportDetailsBoolean(Status),
    "RequirementMet": Status
} if {
    Status := Tenant.PreventExternalUsersFromResharing == true
}
```

### Component 4: Configuration Service Integration

No changes required to SharePointConfigurationService.java - the new property will be automatically captured through the existing TenantProperties mapping in the getSPOTenant() method.

### Component 5: CISA Baseline Documentation

#### sharepoint.md Addition (Section 1: External Sharing)
```markdown
#### MS.SHAREPOINT.1.5v1
**Policy Title**: Ensure external users are prevented from re-sharing files, folders, and sites they do not own

**Criticality**: Shall

**PowerShell Command**:
```powershell
# Audit
Get-SPOTenant | Select-Object PreventExternalUsersFromResharing

# Remediation
Set-SPOTenant -PreventExternalUsersFromResharing $True
```

**CISA Baseline**: This policy prevents external users from re-sharing content they don't own, reducing the risk of uncontrolled data sharing beyond the original sharing intent.

**CIS Control**: 7.2.5 - Ensure external users are prevented from re-sharing files, folders, and sites they do not own
```

---

## 🔄 PowerShell Integration

### Target Command
```powershell
Set-SPOTenant -PreventExternalUsersFromResharing $True
```

### Audit Command
```powershell
Get-SPOTenant | Select-Object PreventExternalUsersFromResharing
```

### Command Mapping
- **SPShellCommand**: `SPShellCommand.PnPTenant.SET()`
- **Property Name**: `"PreventExternalUsersFromResharing"`
- **Expected Value**: `true` (boolean)
- **Validation**: OPA/Rego policy ensures `Tenant.PreventExternalUsersFromResharing == true`

---

## 🧪 Testing Strategy

### Unit Tests
- **SPPreventGuestResharingRemediatorTest.java**
  - Test successful remediation (false → true)
  - Test early exit when already compliant (true → no change)
  - Test rollback functionality (true → false → true)
  - Test error handling (null tenant, command failures)
  - Test parameter change tracking

### Integration Tests
- **SharePoint PowerShell client integration**
- **OPA policy validation integration**
- **Configuration service property capture**
- **End-to-end remediation workflow**

### Test Scenarios
1. **Initial Compliance Check**: Tenant with PreventExternalUsersFromResharing = false
2. **Already Compliant**: Tenant with PreventExternalUsersFromResharing = true
3. **Remediation Success**: Command execution success with parameter tracking
4. **Remediation Failure**: PowerShell command failure handling
5. **Rollback Success**: Successful rollback to previous state
6. **Rollback Failure**: Rollback error handling

---

## 🔒 Security Considerations

### Data Protection
- **Guest Sharing Control**: Prevents external users from extending sharing beyond original scope
- **Access Limitation**: Restricts guest users to view/edit only, removing reshare capability
- **Audit Trail**: Full remediation and rollback tracking for compliance

### Compliance Alignment
- **CIS Benchmark 7.2.5**: Direct implementation of benchmark requirement
- **CISA Guidelines**: Follows established CISA policy structure and validation
- **Syrix Standards**: Adheres to @PolicyRemediator methodology and architecture patterns

---

## 📊 Implementation Risks & Mitigations

### Identified Risks
1. **PowerShell Command Compatibility**: Different SharePoint environments may have variations
   - **Mitigation**: Retry logic with MAX_RETRY (3 attempts)
   - **Validation**: Early testing across environments

2. **Property Mapping**: New property may not be available in older SharePoint versions
   - **Mitigation**: Null safety checks and graceful degradation
   - **Validation**: Version compatibility testing

3. **Rollback Complexity**: Boolean property rollback requires careful state management
   - **Mitigation**: Comprehensive parameter change tracking
   - **Validation**: Extensive rollback testing

### Success Criteria
- ✅ **Functional**: Policy prevents guest resharing when enabled
- ✅ **Performance**: Command execution within 30-second timeout
- ✅ **Reliability**: 99%+ success rate in test environments
- ✅ **Compliance**: OPA validation passes for compliant tenants
- ✅ **Rollback**: 100% successful rollback capability

---

## 🚀 Deployment Plan

### Development Phase
1. ✅ Data model updates (TenantProperties, SharePointTenantProperties, Constants)
2. ✅ @PolicyRemediator implementation with full rollback support
3. ✅ Rego policy validation rule addition
4. ✅ CISA baseline documentation update
5. ✅ Comprehensive unit and integration test suite

### Testing Phase
1. **Unit Testing**: Isolated component testing
2. **Integration Testing**: End-to-end workflow validation
3. **Security Testing**: Policy enforcement validation
4. **Performance Testing**: Command execution timing
5. **Rollback Testing**: State management validation

### Production Deployment
1. **Code Review**: Security and quality assessment
2. **Documentation Review**: Technical and user documentation
3. **Staging Deployment**: Pre-production validation
4. **Production Release**: Gradual rollout with monitoring
5. **Post-Deployment Validation**: Policy effectiveness measurement

---

## 📚 References

- **JIRA Issue**: SYRIX-88 - Restrict Guest Sharing Capabilities in SharePoint
- **CIS Benchmark**: Microsoft 365 Foundations Benchmark v5.0.0, Section 7.2.5
- **CISA Baseline**: sharepoint.md - Section 1: External Sharing
- **Syrix Architecture**: @PolicyRemediator pattern and SPRemediatorBase implementation
- **PowerShell Documentation**: Set-SPOTenant cmdlet reference

---

**Document Version**: 1.0
**Created**: 2025-01-15
**Author**: Syrix JIRA-to-Code Generator v3.8.2
**Review Status**: Ready for Implementation