/.idea/
/.sonarlint/
.DS_Store

SyrixWEB/SyrixPortal/src/.vscode/launch.json
Syrix/.repomixignore
Syrix/repomix-output.md
Syrix/policy_remediators.csv
/Syrix/syrixmsp/node_modules
/syrixmsp/node_modules
/SyrixWEB/SyrixPortal/src/main/resources/static/static/js
/SyrixWEB/SyrixPortal/frontend/playwright-report
/SyrixWEB/SyrixPortal/frontend/test-results
/tests-playwright/node_modules
/SyrixWEB/SyrixPortal/src/main/resources/static/static

/SyrixDocs
/SyrixWEB/SyrixPortal/src/main/resources/static/assets
/SyrixWEB/SyrixPortal/src/main/resources/static/static/media
/SyrixWEB/SyrixPortal/node_modules
/SyrixWEB/SyrixPortal/test-results
/SyrixWEB/SyrixPortal/playwright-report
/Syrix/temp
/SyrixBackend/policy_remediators.csv
/SyrixBackend/config.yml
/SyrixBackend/.repomixignore
/SyrixBackend/Policy Comparison Service - Architecture Document.md
/SyrixBackend/CLAUDE.md
/SyrixBackend/README.md
/SyrixBackend/README-DOC-GENERATOR.md
/SyrixBackend/repomix-output.md
/SyrixBackend/run_tests.sh
/SyrixBackend/Syrix.iml
/SyrixBackend/temp/
/.run
/SyrixWEB/SyrixMSP/node_modules
/SyrixDev/syrix-ip-anomaly
SyrixWEB/SyrixPortal/frontend/CLAUDE_PROMPT_TEMPLATE.md
SyrixWEB/SyrixPortal/frontend/COMPONENT_MIGRATION_LOG_PENDING.md
SyrixWEB/SyrixPortal/frontend/COMPONENT_REPLACEMENT_LOG.md
SyrixWEB/SyrixPortal/frontend/DELETED_BUTTON_MIGRATION_PLAN.md.bak
SyrixWEB/SyrixPortal/frontend/DELETED_BUTTON_MIGRATION_STATUS.md.bak
SyrixWEB/SyrixPortal/frontend/DELETED_COMPONENT_MIGRATION_LOG.md.bak
SyrixWEB/SyrixPortal/frontend/DELETED_COMPONENT_MIGRATION_TRACKER.md.bak
SyrixWEB/SyrixPortal/frontend/install-sass.sh
SyrixWEB/SyrixPortal/frontend/kill-browsers-forcefully.sh
SyrixWEB/SyrixPortal/frontend/kill-browsers.sh
SyrixWEB/SyrixPortal/frontend/remove-failing-tests.sh
SyrixWEB/SyrixPortal/frontend/run-all-tests.sh
SyrixWEB/SyrixPortal/frontend/run-individual-tests.sh
SyrixWEB/SyrixPortal/frontend/run-tests.sh
SyrixWEB/SyrixPortal/frontend/run-verified-tests.sh
SyrixWEB/SyrixPortal/frontend/run-working-tests.sh
SyrixWEB/SyrixPortal/frontend/SHADCN_SETUP.md
/.vscode
SyrixWEB/SyrixPortal/API_MIGRATION_GUIDE.md
SyrixWEB/SyrixPortal/GENERIC_SERVER_CONFIG_GUIDE.md
SyrixWEB/SyrixPortal/JAVA_API_IMPROVEMENTS_TRACKER.md
SyrixWEB/SyrixPortal/API_IMPROVEMENT_PROGRESS.md
SyrixWEB/SyrixPortal/TASK_1_FRONTEND_API_MIGRATION.md
SyrixWEB/SyrixPortal/TASK_2_TOMCAT_REPLACEMENT_GUIDE.md
SyrixWEB/SyrixPortal/TASK_3_UNDERTOW_MIGRATION_STRATEGY.md
SyrixWEB/SyrixPortal/TASK_4_API_UNIT_TESTING_STRATEGY.md
SyrixWEB/SyrixPortal/API_IMPROVEMENT_IMPLEMENTATION_SUMMARY.md
SyrixWEB/SyrixPortal/API_IMPLEMENTATION_CONTINUATION_GUIDE.md
SyrixWEB/SyrixPortal/FRONTEND_MIGRATION_ANALYSIS.md
SyrixWEB/SyrixPortal/validate-api-migration.sh
/SyrixWEB/SyrixPortal/target_test-classes
/SyrixBackend/target_test-classes
API_UNIT_TESTING_IMPLEMENTATION_SUMMARY.md
.vscode/tasks.json
**/asset-manifest.json
/SyrixWEB/SyrixPortal/frontend/
/SyrixWEB/SyrixPortal/target/
SyrixWEB/SyrixPortal/src/main/resources/ddns.p12
SyrixCommon/SyrixMessaging/target/classes/io/syrix/messaging/TaskMessage.class
/SyrixCommon/SyrixBaselinesUtils/target
/SyrixCommon/SyrixMessaging/target
/SyrixDevOps/DockerLocal/mongodb_backup_20250703_130236
/outputs
/configurations
syrix.workspace.code-workspace
/SyrixBackend/outputs
/SyrixCommon/SyrixCommonServices/target/
/SyrixCommon/SyrixDAO/target
/SyrixCommon/SyrixDAO/target_test-classes
**/.chunkhound
**/.claude/settings.local.json
**/.chunkhound.json
**/.env
**/.idea

# React Build Artifacts - Auto-generated files that should not be tracked
/SyrixWEB/SyrixPortal/src/main/resources/static/asset-manifest.json
/SyrixWEB/SyrixPortal/src/main/resources/static/index.html
/SyrixWEB/SyrixPortal/src/main/resources/static/sw.js

# All React build outputs in static directory
/SyrixWEB/SyrixPortal/src/main/resources/static/*.json
/SyrixWEB/SyrixPortal/src/main/resources/static/*.html
/SyrixWEB/SyrixPortal/src/main/resources/static/*.js
/SyrixWEB/SyrixPortal/src/main/resources/static/*.css
/SyrixWEB/SyrixPortal/src/main/resources/static/*.ico
/SyrixWEB/SyrixPortal/src/main/resources/static/*.png
/SyrixWEB/SyrixPortal/src/main/resources/static/*.svg
/SyrixWEB/SyrixPortal/src/main/resources/static/manifest.json
/SyrixWEB/SyrixPortal/src/main/resources/static/robots.txt
/.idea_backup
.claude/**/*.backup-*
.claude/settings.json
.claude/commands/validate-and-fix.md
.claude/commands/spec/validate.md
.claude/commands/spec/execute.md
.claude/commands/spec/decompose.md
.claude/commands/spec/create.md
.claude/commands/research.md
.claude/commands/git/status.md
.claude/commands/git/push.md
.claude/commands/git/ignore-init.md
.claude/commands/git/commit.md
.claude/commands/git/checkout.md
.claude/commands/gh/repo-init.md
.claude/commands/dev/cleanup.md
.claude/commands/create-subagent.md
.claude/commands/create-command.md
.claude/commands/config/bash-timeout.md
.claude/commands/code-review.md
.claude/commands/checkpoint/restore.md
.claude/commands/checkpoint/list.md
.claude/commands/checkpoint/create.md
.claude/commands/agents-md/migration.md
.claude/commands/agents-md/init.md
.claude/commands/agents-md/cli.md
.claude/agents/typescript/typescript-type-expert.md
.claude/agents/typescript/typescript-expert.md
.claude/agents/typescript/typescript-build-expert.md
.claude/agents/triage-expert.md
.claude/agents/testing/vitest-testing-expert.md
.claude/agents/testing/testing-expert.md
.claude/agents/testing/jest-testing-expert.md
.claude/agents/research-expert.md
.claude/agents/refactoring/refactoring-expert.md
.claude/agents/react/react-performance-expert.md
.claude/agents/react/react-expert.md
.claude/agents/oracle.md
.claude/agents/nodejs/nodejs-expert.md
.claude/agents/nestjs-expert.md
.claude/agents/infrastructure/infrastructure-github-actions-expert.md
.claude/agents/infrastructure/infrastructure-docker-expert.md
.claude/agents/git/git-expert.md
.claude/agents/frontend/frontend-css-styling-expert.md
.claude/agents/frontend/frontend-accessibility-expert.md
.claude/agents/framework/framework-nextjs-expert.md
.claude/agents/e2e/e2e-playwright-expert.md
.claude/agents/documentation/documentation-expert.md
.claude/agents/devops/devops-expert.md
.claude/agents/database/database-postgres-expert.md
.claude/agents/database/database-mongodb-expert.md
.claude/agents/database/database-expert.md
.claude/agents/code-search.md
.claude/agents/code-review-expert.md
.claude/agents/code-quality/code-quality-linting-expert.md
.claude/agents/cli-expert.md
.claude/agents/build-tools/build-tools-webpack-expert.md
.claude/agents/build-tools/build-tools-vite-expert.md
.claude/agents/ai-sdk-expert.md
*.bck

/SyrixWEB/SyrixPortal/.yarn
