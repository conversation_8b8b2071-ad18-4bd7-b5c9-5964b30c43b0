package io.syrix.security.config.config;

import io.syrix.security.config.CustomHttpFirewall;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;
import org.springframework.http.HttpMethod;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http, HandlerMappingIntrospector introspector) throws Exception {
        // Create MVC matcher builder
        MvcRequestMatcher.Builder mvcMatcherBuilder = new MvcRequestMatcher.Builder(introspector).servletPath("/");
        
        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .anonymous(anonymous -> anonymous.disable())
            
            // Basic security headers - keeping it simple
            .headers(headers -> headers
                .frameOptions(frameOptions -> frameOptions.deny())
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)
                    .includeSubDomains(true)
                )
            )
            
            .authorizeHttpRequests(auth -> auth
                // Static resources
                .requestMatchers(
                    mvcMatcherBuilder.pattern("/"),
                    mvcMatcherBuilder.pattern("/index.html"),
                    mvcMatcherBuilder.pattern("/favicon.ico"),
                    mvcMatcherBuilder.pattern("/manifest.json"),
                    mvcMatcherBuilder.pattern("/asset-manifest.json"),
                    mvcMatcherBuilder.pattern("/static/**"),
                    mvcMatcherBuilder.pattern("/*.png"),
                    mvcMatcherBuilder.pattern("/*.svg"),
                    mvcMatcherBuilder.pattern("/*.ico"),
                    mvcMatcherBuilder.pattern("/*.js"),
                    mvcMatcherBuilder.pattern("/*.json"),
                    mvcMatcherBuilder.pattern("/*.css"),
                    mvcMatcherBuilder.pattern("/*.txt"),
                    mvcMatcherBuilder.pattern("/*.woff"),
                    mvcMatcherBuilder.pattern("/*.woff2"),
                    mvcMatcherBuilder.pattern("/*.ttf")
                ).permitAll()
                
                // API endpoints - allow all standard HTTP methods
                .requestMatchers(HttpMethod.GET, "/api/**", "/public-api/**").permitAll()
                .requestMatchers(HttpMethod.POST, "/api/**", "/public-api/**").permitAll()
                .requestMatchers(HttpMethod.PUT, "/api/**", "/public-api/**").permitAll()
                .requestMatchers(HttpMethod.DELETE, "/api/**", "/public-api/**").permitAll()
                .requestMatchers(HttpMethod.PATCH, "/api/**", "/public-api/**").permitAll()
                .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                
                // Block only the dangerous TRACE method
                .requestMatchers(HttpMethod.TRACE, "/**").denyAll()
                
                // Health endpoints
                .requestMatchers(HttpMethod.GET, "/ping", "/health.html").permitAll()
                
                // OAuth callback
                .requestMatchers(HttpMethod.GET, "/auth/callback").permitAll()
                
                // SPA routes
                .requestMatchers(HttpMethod.GET, 
                    "/dashboard/**",
                    "/alerts/**",
                    "/notifications/**",
                    "/audit-log/**",
                    "/system-log/**",
                    "/security-checks/**",
                    "/settings/**",
                    "/integrations/**",
                    "/connect/**",
                    "/test/**"
                ).permitAll()
                
                .anyRequest().permitAll()
            )
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
            
        return http.build();
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (web) -> web.httpFirewall(new CustomHttpFirewall());
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of(
            "http://localhost:*",
            "https://localhost:*",
            "https://*.syrix.io"
        ));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", 
            "Content-Type", 
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers"
        ));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
