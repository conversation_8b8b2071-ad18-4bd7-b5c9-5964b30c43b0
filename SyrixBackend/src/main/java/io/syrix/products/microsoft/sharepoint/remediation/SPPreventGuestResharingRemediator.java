package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.PREVENT_EXTERNAL_USERS_FROM_RESHARING_PROPERTY;
import static io.syrix.products.microsoft.sharepoint.SharepointConstants.PREVENT_RESHARING_SUCCESS;

/*
    This policy implements MS.SHAREPOINT.1.5v1 - Ensure external users are prevented from re-sharing files, folders, and sites they do not own.
    Based on CIS MS365 benchmark 7.2.5: Ensure external users are prevented from re-sharing files, folders, and sites they do not own (Automated)

    The policy ensures that external users (guests) cannot re-share SharePoint content beyond the original sharing intent.
    This prevents uncontrolled data sharing and reduces the risk of data leakage through guest accounts.

    PowerShell commands:
    Get-SPOTenant | Select-Object PreventExternalUsersFromResharing
    Set-SPOTenant -PreventExternalUsersFromResharing $true

    Connection: Connect-SPOService -Url https://tenant-admin.sharepoint.com
 */
@PolicyRemediator("MS.SHAREPOINT.1.5v1")
public class SPPreventGuestResharingRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
    private static final Logger log = LoggerFactory.getLogger(SPPreventGuestResharingRemediator.class);

    public SPPreventGuestResharingRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
        super(client, tenant, spConfig);
    }

    // constructor for Rollback interface
    public SPPreventGuestResharingRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        super(client, tenant, null);
    }

    private CompletableFuture<PolicyChangeResult> runCommand(boolean preventResharing, Boolean prevValue) {
        try {
            SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(
                tenant.objectIdentity,
                PREVENT_EXTERNAL_USERS_FROM_RESHARING_PROPERTY,
                preventResharing,
                prevValue
            );

            return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
                    .thenApply(this::checkResult);
        } catch (Exception ex) {
            log.error("Run command for policy {} failed", getPolicyId(), ex);
            return CompletableFuture.failedFuture(ex);
        }
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        // Validate tenant properties are available
        if (tenant == null) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties are not available")
            );
        }

        Boolean currentValue = tenant.preventExternalUsersFromResharing;

        // If already enabled, policy is compliant
        if (Boolean.TRUE.equals(currentValue)) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.success_(getPolicyId(), PREVENT_RESHARING_SUCCESS)
            );
        }

        return runCommand(true, currentValue)
                .exceptionally(ex -> {
                    log.error("Remediate policy {} finished with exception", getPolicyId(), ex);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to prevent guest resharing: " + ex.getMessage());
                });
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        try {
            // Validate rollback parameters
            if (fixResult == null) {
                log.error("Fix result is null for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "No fix result provided for rollback")
                );
            }

            if (fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
                log.warn("No changes found in fix result for policy {}. This likely means the original state was already compliant.", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.success_(getPolicyId(), "No changes to rollback; original state was already compliant.")
                );
            }

            // Validate tenant is still available
            if (tenant == null) {
                log.error("SharePoint tenant properties not available for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties not available for rollback")
                );
            }

            ParameterChangeResult changeResult = fixResult.getChanges().getFirst();

            // Validate that both previous and new values are available for rollback
            if (changeResult.getPrevValue() == null) {
                log.error("Previous value is null for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "Previous value is null, cannot perform rollback")
                );
            }

            if (changeResult.getNewValue() == null) {
                log.error("New value is null for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "New value is null, cannot perform rollback")
                );
            }

            Boolean previousValue = (Boolean) changeResult.getPrevValue();
            Boolean newValue = (Boolean) changeResult.getNewValue();

            return runCommand(previousValue, newValue)
                    .exceptionally(ex -> {
                        log.error("Rollback policy {} finished with exception", getPolicyId(), ex);
                        return IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage());
                    });
        } catch (Exception ex) {
            log.error("Rollback policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage())
            );
        }
    }
}