package io.syrix.products.microsoft.sharepoint;

/**
 * Constants for SharePoint Configuration Service
 */
public class SharepointConstants {
    // Configuration keys
    public static final String CONFIG_KEY_SPO_TENANT = "SPO_tenant";
    public static final String CONFIG_KEY_TENANT_PROP = "TenantProperties";
    public static final String CONFIG_KEY_ALLOW_TO_BE_DELETED_PROP = "AllowToBeDeleted";
    public static final String CONFIG_KEY_SITE_PROP = "SiteProperties";
    
    // Status messages
    public static final String DEFAULT_SUCCESS_MESSAGE = "fixed";
    public static final String SHARING_CAPABILITY_SUCCESS = "Sharing capability fixed";
    public static final String DEFAULT_LINK_PERMISSION_SUCCESS = "Default link permission fixed";
    public static final String DEFAULT_LINK_TYPE_SUCCESS = "Default link type fixed"; 
    public static final String ANONYMOUS_LINK_SUCCESS = "Anonymous link settings fixed";
    public static final String EMAIL_ATTESTATION_SUCCESS = "Email attestation settings fixed";
    public static final String DOMAIN_LIST_SUCCESS = "Domain list settings fixed";
    public static final String CUSTOM_SCRIPT_SUCCESS = "Custom script settings fixed";
    public static final String LINKS_EXPIRE_SUCCESS = "Link expiration settings fixed";
    public static final String ACCOUNT_MATCH_SUCCESS = "Account matching requirement settings fixed";
    public static final String MALWARE_PROTECTION_SUCCESS = "Infected file download protection settings fixed";
    public static final String MODERN_AUTH_SUCCESS = "Modern authentication enforcement settings fixed";
    public static final String AZURE_ADB2B_INTEGRATION_SUCCESS = "Azure AD B2B integration enabled successfully";
    public static final String PREVENT_RESHARING_SUCCESS = "External user resharing prevention settings fixed";

    // Sharing properties
    public static final String SHARING_CAPABILITY_PROPERTY = "SharingCapability";
    public static final String ODB_SHARING_CAPABILITY_PROPERTY = "ODBSharingCapability";
    public static final String SHARING_DOMAIN_RESTRICTION_MODE = "SharingDomainRestrictionMode";
    public static final String DEFAULT_SHARING_LINK_TYPE = "DefaultSharingLinkType";
    public static final String DEFAULT_LINK_PERMISSION = "DefaultLinkPermission";
    public static final String REQUIRE_ANONYMOUS_LINKS_EXPIRE_IN_DAYS = "RequireAnonymousLinksExpireInDays";
    public static final String FILE_ANONYMOUS_LINK_TYPE_PROPERTY = "FileAnonymousLinkType";
    public static final String FOLDER_ANONYMOUS_LINK_TYPE_PROPERTY = "FolderAnonymousLinkType";
    public static final String DEFAULT_SHARING_LINK_TYPE_PROPERTY = "DefaultSharingLinkType";
    public static final String DEFAULT_LINK_PERMISSION_PROPERTY = "DefaultLinkPermission";
    public static final String EMAIL_ATTESTATION_REQUIRED_PROPERTY = "EmailAttestationRequired";
    public static final String EMAIL_ATTESTATION_REAUTH_DAYS_PROPERTY = "EmailAttestationReAuthDays";
    public static final String REQUIRE_ACCEPTING_ACCOUNT_MATCH_INVITED_ACCOUNT_PROPERTY = "RequireAcceptingAccountMatchInvitedAccount";
    
    // Malware protection property - CIS 7.3.1
    public static final String DISALLOW_INFECTED_FILE_DOWNLOAD_PROPERTY = "DisallowInfectedFileDownload";
    // Modern authentication property - CIS 7.2.1
    public static final String LEGACY_AUTH_PROTOCOLS_ENABLED_PROPERTY = "LegacyAuthProtocolsEnabled";
    // Azure AD B2B integration property - CIS 7.2.2
    public static final String ENABLE_AZURE_ADB2B_INTEGRATION_PROPERTY = "EnableAzureADB2BIntegration";
    // Prevent external users from resharing property - CIS 7.2.5
    public static final String PREVENT_EXTERNAL_USERS_FROM_RESHARING_PROPERTY = "PreventExternalUsersFromResharing";
    
    // Sharing values
    public static final String EXTERNAL_USERS_SHARING_ONLY = "ExternalUserSharingOnly";
    public static final String EXTERNAL_SHARING_DISABLED = "Disabled";
    public static final String EXTERNAL_SHARING_EXISTING_EXTERNAL_USER_ONLY = "ExistingExternalUserSharingOnly";
    public static final String EXTERNAL_SHARING_EVERYONE = "ExternalUserAndGuestSharing";
    
    // PowerShell commands
    public static final String SET_SPOSITE_COMMAND = "Set-SPOSite";
    public static final String SET_SPOTENANT_COMMAND = "Set-SPOTenant";
    
    // Common property names
    public static final String OBJECT_IDENTITY_PROPERTY = "objectIdentity";
    public static final String IDENTITY_PROPERTY = "Identity";
    public static final String URL_PROPERTY = "Url";
}
