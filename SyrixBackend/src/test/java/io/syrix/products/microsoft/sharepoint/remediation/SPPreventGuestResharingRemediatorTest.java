package io.syrix.products.microsoft.sharepoint.remediation;

import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.ShellCommandResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SPPreventGuestResharingRemediatorTest {

    @Mock
    private PowerShellSharepointClient mockClient;

    @Mock
    private SharepointRemediationConfig mockConfig;

    private SharePointTenantProperties tenantProperties;
    private SPPreventGuestResharingRemediator remediator;

    @BeforeEach
    void setUp() {
        tenantProperties = new SharePointTenantProperties();
        tenantProperties.objectIdentity = "test-tenant";
        tenantProperties.preventExternalUsersFromResharing = false; // Default non-compliant state

        remediator = new SPPreventGuestResharingRemediator(mockClient, tenantProperties, mockConfig);
    }

    @Test
    void testGetPolicyId() {
        assertEquals("MS.SHAREPOINT.1.5v1", remediator.getPolicyId());
    }

    @Test
    void testRemediateWhenAlreadyCompliant() throws Exception {
        // Set tenant to already compliant state
        tenantProperties.preventExternalUsersFromResharing = true;

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult policyResult = result.get();

        assertEquals(RemediationResult.SUCCESS, policyResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", policyResult.getPolicyId());
        assertNotNull(policyResult.getDesc());

        // No PowerShell commands should be executed
        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRemediateWhenNotCompliant() throws Exception {
        // Mock successful PowerShell execution
        GeneralResult successResult = new GeneralResult();
        successResult.errorInfo = null; // No error indicates success
        successResult.traceCorrelationId = "test-correlation-id";

        when(mockClient.execute_(any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            ShellCommandResult<GeneralResult> result = ShellCommandResult.of(List.of(successResult), invocation.getArgument(0));
            return CompletableFuture.completedFuture(result);
        });

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult policyResult = result.get();

        assertEquals(RemediationResult.SUCCESS, policyResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", policyResult.getPolicyId());

        verify(mockClient, times(1)).execute_(any());
    }

    @Test
    void testRemediateWithPowerShellError() throws Exception {
        // Mock PowerShell execution with error
        GeneralResult errorResult = new GeneralResult();
        errorResult.errorInfo = new GeneralResult.ErrorInfo();
        errorResult.errorInfo.errorMessage = "Access denied";

        when(mockClient.execute_(any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            ShellCommandResult<GeneralResult> result = ShellCommandResult.of(List.of(errorResult), invocation.getArgument(0));
            return CompletableFuture.completedFuture(result);
        });

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult policyResult = result.get();

        assertEquals(RemediationResult.FAILED, policyResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", policyResult.getPolicyId());
        assertTrue(policyResult.getDesc().contains("Access denied"));

        verify(mockClient, times(1)).execute_(any());
    }

    @Test
    void testRemediateWithNullTenant() throws Exception {
        SPPreventGuestResharingRemediator nullTenantRemediator =
            new SPPreventGuestResharingRemediator(mockClient, null, mockConfig);

        CompletableFuture<PolicyChangeResult> result = nullTenantRemediator.remediate_();
        PolicyChangeResult policyResult = result.get();

        assertEquals(RemediationResult.FAILED, policyResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", policyResult.getPolicyId());
        assertTrue(policyResult.getDesc().contains("SharePoint tenant properties are not available"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRemediateWithException() throws Exception {
        when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Connection failed")));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult policyResult = result.get();

        assertEquals(RemediationResult.FAILED, policyResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", policyResult.getPolicyId());
        assertTrue(policyResult.getDesc().contains("Failed to prevent guest resharing"));

        verify(mockClient, times(3)).execute_(any());
    }

    @Test
    void testRollbackSuccess() throws Exception {
        // Create a valid fix result to rollback
        ParameterChangeResult change = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true)
                .parameter("PreventExternalUsersFromResharing")
                .status(ParameterChangeStatus.SUCCESS);

        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList(change));

        // Mock successful rollback execution
        GeneralResult successResult = new GeneralResult();
        successResult.errorInfo = null;
        successResult.traceCorrelationId = "rollback-correlation-id";

        when(mockClient.execute_(any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            ShellCommandResult<GeneralResult> result = ShellCommandResult.of(List.of(successResult), invocation.getArgument(0));
            return CompletableFuture.completedFuture(result);
        });

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.SUCCESS, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());

        verify(mockClient, times(1)).execute_(any());
    }

    @Test
    void testRollbackWithNullFixResult() throws Exception {
        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(null);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("No fix result provided for rollback"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRollbackWithNoChanges() throws Exception {
        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList()); // Empty changes

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("No changes to rollback"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRollbackWithNullPrevValue() throws Exception {
        ParameterChangeResult change = new ParameterChangeResult()
                .prevValue(null) // Null previous value
                .newValue(true)
                .parameter("PreventExternalUsersFromResharing");

        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList(change));

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("Previous value is null"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRollbackWithNullNewValue() throws Exception {
        ParameterChangeResult change = new ParameterChangeResult()
                .prevValue(false)
                .newValue(null) // Null new value
                .parameter("PreventExternalUsersFromResharing");

        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList(change));

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("New value is null"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRollbackWithNullTenant() throws Exception {
        SPPreventGuestResharingRemediator nullTenantRemediator =
            new SPPreventGuestResharingRemediator(mockClient, null);

        ParameterChangeResult change = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true)
                .parameter("PreventExternalUsersFromResharing");

        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList(change));

        CompletableFuture<PolicyChangeResult> rollbackResult = nullTenantRemediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("SharePoint tenant properties not available for rollback"));

        verify(mockClient, never()).execute_(any());
    }

    @Test
    void testRollbackWithException() throws Exception {
        ParameterChangeResult change = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true)
                .parameter("PreventExternalUsersFromResharing");

        PolicyChangeResult fixResult = new PolicyChangeResult()
                .policyId("MS.SHAREPOINT.1.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(Arrays.asList(change));

        when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(fixResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
        assertEquals("MS.SHAREPOINT.1.5v1", rollbackChangeResult.getPolicyId());
        assertTrue(rollbackChangeResult.getDesc().contains("Rollback failed"));

        verify(mockClient, times(3)).execute_(any());
    }

    @Test
    void testRemediateJsonConversion() throws Exception {
        // Test the remediate() method that returns JsonNode
        tenantProperties.preventExternalUsersFromResharing = true; // Already compliant

        CompletableFuture<com.fasterxml.jackson.databind.JsonNode> result = remediator.remediate();
        com.fasterxml.jackson.databind.JsonNode jsonResult = result.get();

        assertNotNull(jsonResult);
        assertTrue(jsonResult.isObject());
        // Basic validation that JSON conversion worked
        assertTrue(jsonResult.has("result") || jsonResult.has("Result"));
    }
}